"""
Configuration file for RL Portfolio Rebalancing System
"""

import os
from typing import List, Dict, Any, Optional, Tuple
from enum import Enum

# Data Configuration
DATA_CONFIG = {
    "etf_symbols": ["VT", "IEF", "REET", "GLD", "COM"],  # 5 ETFs as specified
    "risk_free_symbol": "^TNX",  # Used for risk-free rate in performance calculations (not as RL feature)
    "start_date": "20XX-01-01",
    "frequency": "D",  # Daily data fetching
    "rebalancing_frequency": "M",  # Monthly rebalancing
    "data_source": "yfinance"
}

# Trading Configuration
TRADING_CONFIG = {
    "initial_cash": 100000,
    "transaction_cost": 0.001,  # 0.1%
    "slippage_range": [0.0, 0.01],
    "max_allowed_loss": 0.6
}

# TensorTrade Configuration
TENSORTRADE_CONFIG = {
    "window_size": 21,  # Variable window size for OHLCV data processing (only Close prices used as features)
    "enable_logger": True,
    "action_scheme": "portfolio-weights",
    "reward_scheme": "sharpe-ratio",
    "cost_penalty_lambda": 0.2  # Penalty coefficient for turnover rate to reduce overfitting (0.1-0.5 range)
}

# Training Configuration
TRAINING_CONFIG = {
    "algorithm": "PPO",
    "total_timesteps": 141000,  
    "learning_rate": 3e-4,
    "batch_size": 64,
    "policy_layers": [256, 256],
    "clip_range": 0.2,
    "value_function_coeff": 0.5,
    "entropy_coeff": 0.01
}

# Evaluation Configuration
EVALUATION_CONFIG = {
    "sharpe_window": 12,  # 12 months for Sharpe calculation
    "benchmark_strategy": "equal_weight",
    "performance_metrics": [
        "total_return",
        "annualized_return", 
        "volatility",
        "sharpe_ratio",
        "max_drawdown",
        "calmar_ratio"
    ]
}

# Technical Indicators Configuration - ENABLED for RSI14
# Technical indicators re-enabled to include RSI14 features alongside Close price features
# OHLCV data is fetched for completeness and RSI14 is calculated from Close prices
TECHNICAL_INDICATORS_CONFIG = {
    "enabled": True,  # Technical indicators enabled for RSI14
    "indicators": {
        "rsi14": {
            "enabled": True,
            "window": 14,
            "fillna": False  # Handle NaN values explicitly in preprocessing
        }
    },
    "note": "RSI14 technical indicator added alongside Close price features"
}

# Logging Configuration
LOGGING_CONFIG = {
    "level": "DEBUG",
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s",
    "date_format": "%Y-%m-%d %H:%M:%S",
    "log_to_file": True,
    "log_directory": "logs",
    
    # Unicode Logging Configuration
    "unicode_mode": "auto",  # "auto", "unicode", "ascii"
    "fallback_on_error": True,
    "debug_encoding_issues": False,
    "character_mapping": {
        # Default character mappings (can be overridden)
        "✅": "[OK]",
        "❌": "[FAIL]",
        "🎯": "[MILESTONE]",
        "💻": "[RESOURCE]",
        "🚀": "[PHASE]",
        "⚠️": "[WARNING]",
        "📊": "[DATA]",
        "📈": "[METRICS]",
        "🏥": "[HEALTH]",
        "🔢": "[COUNT]"
    }
}

# Directory Configuration
DIRECTORY_CONFIG = {
    "data": "data",
    "models": "models", 
    "logs": "logs",
    "results": "results",
    "config": "config"
}

# Mode-Specific Configuration
MODE_CONFIG = {
    "training": {
        "data_split_ratio": 0.8,
        "output_settings": {
            "generate_csv": False,
            "generate_json": False,
            "generate_reports": False,
            "file_prefix": "training_",
            "timestamp_format": "%Y%m%d_%H%M%S"
        },
        "validation_requirements": {
            "require_data": True,
            "require_model": False,
            "min_data_points": 750
        },
        "execution_settings": {
            "skip_evaluation": True,
            "skip_training": False,
            "save_model": True,
            "enable_tensorboard": True,
            "checkpoint_frequency": 500
        }
    },
    "evaluation": {
        "data_split_ratio": 0.8,
        "output_settings": {
            "generate_csv": True,
            "generate_json": True,
            "generate_reports": True,
            "file_prefix": "evaluation_",
            "timestamp_format": "%Y%m%d_%H%M%S"
        },
        "validation_requirements": {
            "require_data": True,
            "require_model": True,
            "min_data_points": 150
        },
        "execution_settings": {
            "skip_evaluation": False,
            "skip_training": True,
            "save_model": False,
            "enable_tensorboard": False,
            "checkpoint_frequency": 500,
            "load_model_path": None  # Auto-detect if None
        }
    },
    "full": {
        "data_split_ratio": 0.8,
        "output_settings": {
            "generate_csv": True,
            "generate_json": True,
            "generate_reports": True,
            "file_prefix": "",
            "timestamp_format": "%Y%m%d_%H%M%S"
        },
        "validation_requirements": {
            "require_data": True,
            "require_model": False,
            "min_data_points": 750
        },
        "execution_settings": {
            "skip_evaluation": False,
            "skip_training": False,
            "save_model": True,
            "enable_tensorboard": True,
            "checkpoint_frequency": 500
        }
    }
}

# Unicode Logging Enums and Validation

class LoggingMode(Enum):
    """Enumeration for Unicode logging modes"""
    AUTO = "auto"
    UNICODE = "unicode"
    ASCII = "ascii"
    SAFE = "safe"  # ASCII with enhanced visual markers


def get_unicode_logging_config() -> Dict[str, Any]:
    """
    Get Unicode logging configuration with environment variable overrides.
    
    Returns:
        Dict containing Unicode logging configuration with environment overrides applied
    """
    config = LOGGING_CONFIG.copy()
    
    # Environment variable overrides
    env_mode = os.getenv("RL_PORTFOLIO_UNICODE_MODE")
    if env_mode:
        config["unicode_mode"] = env_mode
    
    env_fallback = os.getenv("RL_PORTFOLIO_UNICODE_FALLBACK")
    if env_fallback:
        config["fallback_on_error"] = env_fallback.lower() in ("true", "1", "yes", "on")
    
    env_debug = os.getenv("RL_PORTFOLIO_UNICODE_DEBUG")
    if env_debug:
        config["debug_encoding_issues"] = env_debug.lower() in ("true", "1", "yes", "on")
    
    return config


def validate_unicode_logging_config(config: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validate Unicode logging configuration and apply defaults for invalid values.
    
    Args:
        config: Configuration dictionary to validate
        
    Returns:
        Validated configuration dictionary with corrected values
        
    Raises:
        Warning messages are logged for invalid configurations
    """
    validated_config = config.copy()
    
    # Validate unicode_mode
    valid_modes = {mode.value for mode in LoggingMode}
    if config.get("unicode_mode") not in valid_modes:
        print(f"[WARNING] Invalid unicode_mode '{config.get('unicode_mode')}'. Using 'auto' mode.")
        validated_config["unicode_mode"] = "auto"
    
    # Validate boolean values
    if not isinstance(config.get("fallback_on_error"), bool):
        print(f"[WARNING] Invalid fallback_on_error value. Using default: True")
        validated_config["fallback_on_error"] = True
    
    if not isinstance(config.get("debug_encoding_issues"), bool):
        print(f"[WARNING] Invalid debug_encoding_issues value. Using default: False")
        validated_config["debug_encoding_issues"] = False
    
    # Validate character_mapping
    if not isinstance(config.get("character_mapping"), dict):
        print(f"[WARNING] Invalid character_mapping. Using default mappings.")
        validated_config["character_mapping"] = LOGGING_CONFIG["character_mapping"].copy()
    
    return validated_config


def get_validated_unicode_logging_config() -> Dict[str, Any]:
    """
    Get Unicode logging configuration with environment overrides and validation applied.
    
    Returns:
        Fully validated Unicode logging configuration
    """
    config = get_unicode_logging_config()
    return validate_unicode_logging_config(config)


def update_character_mapping(custom_mappings: Dict[str, str]) -> None:
    """
    Update the character mapping configuration with custom mappings.
    
    Args:
        custom_mappings: Dictionary of Unicode character to ASCII replacement mappings
    """
    LOGGING_CONFIG["character_mapping"].update(custom_mappings)


def get_logging_mode_from_config() -> LoggingMode:
    """
    Get the current logging mode from configuration.
    
    Returns:
        LoggingMode enum value
    """
    config = get_validated_unicode_logging_config()
    mode_str = config["unicode_mode"]
    
    try:
        return LoggingMode(mode_str)
    except ValueError:
        print(f"[WARNING] Invalid logging mode '{mode_str}'. Defaulting to AUTO.")
        return LoggingMode.AUTO


def get_mode_config_with_env_overrides(mode: str) -> Dict[str, Any]:
    """
    Get mode-specific configuration with environment variable overrides applied.
    
    Args:
        mode: Execution mode ('training', 'evaluation', 'full')
        
    Returns:
        Dict containing mode configuration with environment overrides applied
        
    Raises:
        ValueError: If mode is not supported
    """
    if mode not in MODE_CONFIG:
        raise ValueError(f"Unsupported mode: {mode}. Valid modes: {list(MODE_CONFIG.keys())}")
    
    # Start with base mode configuration
    config = MODE_CONFIG[mode].copy()
    
    # Apply environment variable overrides
    env_data_split_ratio = os.getenv("RL_PORTFOLIO_DATA_SPLIT_RATIO")
    if env_data_split_ratio:
        try:
            ratio = float(env_data_split_ratio)
            if 0.0 < ratio < 1.0:
                config["data_split_ratio"] = ratio
            else:
                print(f"[WARNING] Invalid data split ratio '{env_data_split_ratio}'. Must be between 0.0 and 1.0.")
        except ValueError:
            print(f"[WARNING] Invalid data split ratio '{env_data_split_ratio}'. Must be a float.")
    
    env_output_dir = os.getenv("RL_PORTFOLIO_OUTPUT_DIR")
    if env_output_dir:
        config["output_settings"]["output_directory"] = env_output_dir
    
    env_model_path = os.getenv("RL_PORTFOLIO_MODEL_PATH")
    if env_model_path and mode == "evaluation":
        config["execution_settings"]["load_model_path"] = env_model_path
    
    env_skip_validation = os.getenv("RL_PORTFOLIO_SKIP_VALIDATION")
    if env_skip_validation:
        config["skip_validation"] = env_skip_validation.lower() in ("true", "1", "yes", "on")
    
    return config


def validate_mode_config(mode: str, config: Dict[str, Any], silent: bool = False) -> Dict[str, Any]:
    """
    Validate mode-specific configuration and apply defaults for invalid values.
    
    Args:
        mode: Execution mode
        config: Configuration dictionary to validate
        silent: If True, suppress warning messages
        
    Returns:
        Validated configuration dictionary with corrected values
        
    Raises:
        ValueError: If critical configuration values are invalid
    """
    import copy
    validated_config = copy.deepcopy(config)
    
    # Validate data split ratio
    split_ratio = config.get("data_split_ratio", 0.8)
    if not isinstance(split_ratio, (int, float)) or not 0.0 < split_ratio < 1.0:
        if not silent:
            print(f"[WARNING] Invalid data_split_ratio '{split_ratio}' for mode '{mode}'. Using default: 0.8")
        validated_config["data_split_ratio"] = 0.8
    
    # Validate output settings
    output_settings = config.get("output_settings", {})
    if not isinstance(output_settings, dict):
        if not silent:
            print(f"[WARNING] Invalid output_settings for mode '{mode}'. Using defaults.")
        validated_config["output_settings"] = MODE_CONFIG[mode]["output_settings"].copy()
    else:
        # Ensure output_settings exists in validated_config
        if "output_settings" not in validated_config:
            validated_config["output_settings"] = {}
        
        # Validate individual output settings
        for key in ["generate_csv", "generate_json", "generate_reports"]:
            if key in output_settings and not isinstance(output_settings[key], bool):
                if not silent:
                    print(f"[WARNING] Invalid {key} value for mode '{mode}'. Must be boolean.")
                validated_config["output_settings"][key] = MODE_CONFIG[mode]["output_settings"][key]
            elif key not in output_settings:
                validated_config["output_settings"][key] = MODE_CONFIG[mode]["output_settings"][key]
        
        for key in ["file_prefix", "timestamp_format"]:
            if key in output_settings and not isinstance(output_settings[key], str):
                if not silent:
                    print(f"[WARNING] Invalid {key} value for mode '{mode}'. Must be string.")
                validated_config["output_settings"][key] = MODE_CONFIG[mode]["output_settings"][key]
            elif key not in output_settings:
                validated_config["output_settings"][key] = MODE_CONFIG[mode]["output_settings"][key]
    
    # Validate validation requirements
    validation_reqs = config.get("validation_requirements", {})
    if not isinstance(validation_reqs, dict):
        if not silent:
            print(f"[WARNING] Invalid validation_requirements for mode '{mode}'. Using defaults.")
        validated_config["validation_requirements"] = MODE_CONFIG[mode]["validation_requirements"].copy()
    else:
        # Ensure validation_requirements exists in validated_config
        if "validation_requirements" not in validated_config:
            validated_config["validation_requirements"] = {}
        
        # Validate individual validation requirements
        for key in ["require_data", "require_model"]:
            if key in validation_reqs and not isinstance(validation_reqs[key], bool):
                if not silent:
                    print(f"[WARNING] Invalid {key} value for mode '{mode}'. Must be boolean.")
                validated_config["validation_requirements"][key] = MODE_CONFIG[mode]["validation_requirements"][key]
            elif key not in validation_reqs:
                validated_config["validation_requirements"][key] = MODE_CONFIG[mode]["validation_requirements"][key]
        
        min_data_points = validation_reqs.get("min_data_points")
        if min_data_points is not None and (not isinstance(min_data_points, int) or min_data_points <= 0):
            if not silent:
                print(f"[WARNING] Invalid min_data_points '{min_data_points}' for mode '{mode}'. Must be positive integer.")
            validated_config["validation_requirements"]["min_data_points"] = MODE_CONFIG[mode]["validation_requirements"]["min_data_points"]
        elif "min_data_points" not in validation_reqs:
            validated_config["validation_requirements"]["min_data_points"] = MODE_CONFIG[mode]["validation_requirements"]["min_data_points"]
    
    # Validate execution settings
    execution_settings = config.get("execution_settings", {})
    if not isinstance(execution_settings, dict):
        if not silent:
            print(f"[WARNING] Invalid execution_settings for mode '{mode}'. Using defaults.")
        validated_config["execution_settings"] = MODE_CONFIG[mode]["execution_settings"].copy()
    else:
        # Ensure execution_settings exists in validated_config
        if "execution_settings" not in validated_config:
            validated_config["execution_settings"] = {}
        
        # Validate individual execution settings
        for key in ["skip_evaluation", "skip_training", "save_model", "enable_tensorboard"]:
            if key in execution_settings and not isinstance(execution_settings[key], bool):
                if not silent:
                    print(f"[WARNING] Invalid {key} value for mode '{mode}'. Must be boolean.")
                validated_config["execution_settings"][key] = MODE_CONFIG[mode]["execution_settings"][key]
            elif key not in execution_settings:
                validated_config["execution_settings"][key] = MODE_CONFIG[mode]["execution_settings"][key]
        
        checkpoint_freq = execution_settings.get("checkpoint_frequency")
        if checkpoint_freq is not None and (not isinstance(checkpoint_freq, int) or checkpoint_freq <= 0):
            if not silent:
                print(f"[WARNING] Invalid checkpoint_frequency '{checkpoint_freq}' for mode '{mode}'. Must be positive integer.")
            validated_config["execution_settings"]["checkpoint_frequency"] = MODE_CONFIG[mode]["execution_settings"]["checkpoint_frequency"]
        elif "checkpoint_frequency" not in execution_settings:
            validated_config["execution_settings"]["checkpoint_frequency"] = MODE_CONFIG[mode]["execution_settings"]["checkpoint_frequency"]
    
    return validated_config


def get_validated_mode_config(mode: str, silent: bool = False) -> Dict[str, Any]:
    """
    Get mode-specific configuration with environment overrides and validation applied.
    
    Args:
        mode: Execution mode ('training', 'evaluation', 'full')
        silent: If True, suppress warning messages
        
    Returns:
        Fully validated mode configuration
        
    Raises:
        ValueError: If mode is not supported
    """
    config = get_mode_config_with_env_overrides(mode)
    return validate_mode_config(mode, config, silent)


def get_execution_mode_from_env() -> Optional[str]:
    """
    Get execution mode from environment variable.
    
    Returns:
        Mode string if set and valid, None otherwise
    """
    env_mode = os.getenv("RL_PORTFOLIO_MODE")
    if env_mode and env_mode.lower() in MODE_CONFIG:
        return env_mode.lower()
    elif env_mode:
        print(f"[WARNING] Invalid mode in RL_PORTFOLIO_MODE: '{env_mode}'. Valid modes: {list(MODE_CONFIG.keys())}")
    return None


# Environment variable documentation
UNICODE_LOGGING_ENV_VARS = {
    "RL_PORTFOLIO_UNICODE_MODE": "Override unicode logging mode (auto|unicode|ascii)",
    "RL_PORTFOLIO_UNICODE_FALLBACK": "Enable/disable fallback on encoding errors (true|false)",
    "RL_PORTFOLIO_UNICODE_DEBUG": "Enable/disable encoding debug logging (true|false)"
}

# Mode-specific environment variables
MODE_ENV_VARS = {
    "RL_PORTFOLIO_MODE": "Override execution mode (training|evaluation|full)",
    "RL_PORTFOLIO_MODEL_PATH": "Override model path for evaluation mode",
    "RL_PORTFOLIO_DATA_SPLIT_RATIO": "Override data split ratio (0.0-1.0)",
    "RL_PORTFOLIO_OUTPUT_DIR": "Override output directory path",
    "RL_PORTFOLIO_SKIP_VALIDATION": "Skip mode validation (true|false) - for testing only"
}

# Combined environment variables documentation
ALL_ENV_VARS = {**UNICODE_LOGGING_ENV_VARS, **MODE_ENV_VARS}


class ConfigManager:
    """
    Centralized configuration manager for mode-specific settings.
    
    Provides a unified interface for accessing all configuration settings
    with mode-specific overrides and environment variable support.
    """
    
    def __init__(self, mode: str = "full"):
        """
        Initialize the configuration manager.
        
        Args:
            mode: Execution mode ('training', 'evaluation', 'full')
        """
        self.mode = mode.lower()
        self._mode_config = get_validated_mode_config(self.mode)
        self._unicode_config = get_validated_unicode_logging_config()
    
    @property
    def data_split_ratio(self) -> float:
        """Get the data split ratio for the current mode."""
        return self._mode_config["data_split_ratio"]
    
    @property
    def output_settings(self) -> Dict[str, Any]:
        """Get output settings for the current mode."""
        return self._mode_config["output_settings"]
    
    @property
    def validation_requirements(self) -> Dict[str, Any]:
        """Get validation requirements for the current mode."""
        return self._mode_config["validation_requirements"]
    
    @property
    def execution_settings(self) -> Dict[str, Any]:
        """Get execution settings for the current mode."""
        return self._mode_config["execution_settings"]
    
    @property
    def should_generate_csv(self) -> bool:
        """Check if CSV files should be generated for the current mode."""
        return self.output_settings.get("generate_csv", False)
    
    @property
    def should_generate_json(self) -> bool:
        """Check if JSON files should be generated for the current mode."""
        return self.output_settings.get("generate_json", False)
    
    @property
    def should_generate_reports(self) -> bool:
        """Check if reports should be generated for the current mode."""
        return self.output_settings.get("generate_reports", False)
    
    @property
    def should_skip_training(self) -> bool:
        """Check if training should be skipped for the current mode."""
        return self.execution_settings.get("skip_training", False)
    
    @property
    def should_skip_evaluation(self) -> bool:
        """Check if evaluation should be skipped for the current mode."""
        return self.execution_settings.get("skip_evaluation", False)
    
    @property
    def should_save_model(self) -> bool:
        """Check if model should be saved for the current mode."""
        return self.execution_settings.get("save_model", True)
    
    @property
    def file_prefix(self) -> str:
        """Get the file prefix for the current mode."""
        return self.output_settings.get("file_prefix", "")
    
    @property
    def timestamp_format(self) -> str:
        """Get the timestamp format for the current mode."""
        return self.output_settings.get("timestamp_format", "%Y%m%d_%H%M%S")
    
    def get_output_directory(self, base_dir: str = None) -> str:
        """
        Get the output directory for the current mode.
        
        Args:
            base_dir: Base directory to use if not overridden
            
        Returns:
            Output directory path
        """
        return self.output_settings.get("output_directory", base_dir or DIRECTORY_CONFIG["results"])
    
    def get_model_path(self) -> Optional[str]:
        """Get the model path for evaluation mode."""
        if self.mode == "evaluation":
            return self.execution_settings.get("load_model_path")
        return None
    
    def requires_model(self) -> bool:
        """Check if the current mode requires a trained model."""
        return self.validation_requirements.get("require_model", False)
    
    def requires_data(self) -> bool:
        """Check if the current mode requires data."""
        return self.validation_requirements.get("require_data", True)
    
    def get_min_data_points(self) -> int:
        """Get the minimum required data points for the current mode."""
        return self.validation_requirements.get("min_data_points", 750)
    
    def should_enable_tensorboard(self) -> bool:
        """Check if TensorBoard should be enabled for the current mode."""
        return self.execution_settings.get("enable_tensorboard", False)
    
    def get_checkpoint_frequency(self) -> int:
        """Get the checkpoint frequency for the current mode."""
        return self.execution_settings.get("checkpoint_frequency", 500)
    
    def get_all_config(self) -> Dict[str, Any]:
        """
        Get all configuration settings as a dictionary.
        
        Returns:
            Dictionary containing all configuration settings
        """
        return {
            "mode": self.mode,
            "data": DATA_CONFIG,
            "trading": TRADING_CONFIG,
            "tensortrade": TENSORTRADE_CONFIG,
            "training": TRAINING_CONFIG,
            "evaluation": EVALUATION_CONFIG,

            "logging": LOGGING_CONFIG,
            "directories": DIRECTORY_CONFIG,
            "mode_specific": self._mode_config,
            "unicode_logging": self._unicode_config
        }
    
    def validate_mode_requirements(self) -> Tuple[bool, List[str]]:
        """
        Validate that all requirements for the current mode are met.
        
        Returns:
            Tuple of (is_valid, list_of_error_messages)
        """
        errors = []
        
        # Check data requirements
        if self.requires_data():
            # This would need to be implemented with actual data checking logic
            # For now, we'll assume data validation happens elsewhere
            pass
        
        # Check model requirements
        if self.requires_model():
            model_path = self.get_model_path()
            if model_path and not os.path.exists(model_path):
                errors.append(f"Required model file not found: {model_path}")
            elif not model_path:
                # Check for auto-discoverable models in models directory
                models_dir = DIRECTORY_CONFIG["models"]
                if not os.path.exists(models_dir) or not os.listdir(models_dir):
                    errors.append(f"No trained models found in {models_dir} directory")
        
        # Check output directory
        output_dir = self.get_output_directory()
        if not os.path.exists(output_dir):
            try:
                os.makedirs(output_dir, exist_ok=True)
            except OSError as e:
                errors.append(f"Cannot create output directory {output_dir}: {e}")
        
        return len(errors) == 0, errors
    
    def __str__(self) -> str:
        """String representation of the configuration manager."""
        return f"ConfigManager(mode={self.mode}, data_split_ratio={self.data_split_ratio})"
    
    def __repr__(self) -> str:
        """Detailed string representation of the configuration manager."""
        return (f"ConfigManager(mode={self.mode}, "
                f"data_split_ratio={self.data_split_ratio}, "
                f"generate_csv={self.should_generate_csv}, "
                f"skip_training={self.should_skip_training}, "
                f"skip_evaluation={self.should_skip_evaluation})")


# Convenience function to create a ConfigManager instance
def create_config_manager(mode: str = None) -> ConfigManager:
    """
    Create a ConfigManager instance with optional mode override.
    
    Args:
        mode: Execution mode. If None, tries to get from environment or defaults to 'full'
        
    Returns:
        ConfigManager instance
    """
    if mode is None:
        mode = get_execution_mode_from_env() or "full"
    
    return ConfigManager(mode)


# Legacy compatibility - create a default CONFIG object
# This maintains backward compatibility with existing code
class CONFIG:
    """Legacy configuration object for backward compatibility."""
    
    DATA = type('DATA', (), DATA_CONFIG)()
    TRADING = type('TRADING', (), TRADING_CONFIG)()
    TENSORTRADE = type('TENSORTRADE', (), TENSORTRADE_CONFIG)()
    TRAINING = type('TRAINING', (), TRAINING_CONFIG)()
    EVALUATION = type('EVALUATION', (), EVALUATION_CONFIG)()

    LOGGING = type('LOGGING', (), LOGGING_CONFIG)()
    DIRECTORIES = type('DIRECTORIES', (), DIRECTORY_CONFIG)()
    
    # Add mode-specific access
    @staticmethod
    def get_mode_config(mode: str) -> ConfigManager:
        """Get a ConfigManager instance for the specified mode."""
        return ConfigManager(mode)


# Export commonly used configurations for easy access
__all__ = [
    'DATA_CONFIG', 'TRADING_CONFIG', 'TENSORTRADE_CONFIG', 'TRAINING_CONFIG',
    'EVALUATION_CONFIG', 'LOGGING_CONFIG',
    'DIRECTORY_CONFIG', 'MODE_CONFIG', 'CONFIG', 'ConfigManager',
    'create_config_manager', 'get_validated_mode_config', 'get_execution_mode_from_env',
    'LoggingMode', 'get_validated_unicode_logging_config', 'ALL_ENV_VARS'
]